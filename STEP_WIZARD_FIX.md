# 分步表单修复说明

## 问题描述

用户选择应用后，分步表单直接跳转到最后一步，没有显示应用配置步骤。

## 问题原因

1. **步骤数组动态变化**: `hasAppConfig` 状态变化时，`steps` 数组长度会改变，但 `currentStep` 没有相应调整
2. **步骤渲染逻辑不完整**: 缺少对 `currentStep === 2 && hasAppConfig` 情况的处理
3. **按钮逻辑错误**: 最后一步的判断条件不正确

## 修复方案

### 1. 使用 useMemo 优化步骤数组生成

```javascript
const steps = useMemo(() => {
  const baseSteps = [
    {
      title: '确认配置',
      description: '确认设备和应用信息',
      icon: <Server className="w-4 h-4" />,
    },
  ];

  if (hasAppConfig) {
    baseSteps.push({
      title: '应用配置',
      description: '配置应用参数',
      icon: <Settings className="w-4 h-4" />,
    });
  }

  baseSteps.push({
    title: '订单确认',
    description: '确认订单并支付',
    icon: <CheckCircle className="w-4 h-4" />,
  });

  return baseSteps;
}, [hasAppConfig]);
```

### 2. 添加步骤状态同步

```javascript
// 当应用配置状态变化时，重置步骤（如果当前不在第一步）
React.useEffect(() => {
  // 如果当前在第二步或第三步，且应用配置状态发生变化，重置到第一步
  if (currentStep > 0) {
    const expectedStepCount = hasAppConfig ? 3 : 2;
    if (steps.length !== expectedStepCount) {
      setCurrentStep(0);
    }
  }
}, [hasAppConfig, currentStep, steps.length]);
```

### 3. 修复步骤导航逻辑

```javascript
const handleNext = async () => {
  if (currentStep === 0) {
    // 第一步：验证基础配置
    try {
      const fieldsToValidate = ['instanceName', 'duration', 'autoRenew'];
      if (application) {
        fieldsToValidate.push('autoInstallApp');
      }
      const values = await form.validateFields(fieldsToValidate);
      setOrderConfig(prev => ({ ...prev, ...values }));
      setCurrentStep(1);
    } catch (error) {
      console.error('表单验证失败:', error);
    }
  } else if (currentStep === 1 && hasAppConfig) {
    // 第二步：验证应用配置（仅当有应用配置时）
    try {
      const appConfigValues = await form.validateFields();
      setOrderConfig(prev => ({
        ...prev,
        appConfig: appConfigValues,
      }));
      setCurrentStep(2);
    } catch (error) {
      console.error('应用配置验证失败:', error);
    }
  }
};
```

### 4. 修复步骤内容渲染

```javascript
{/* 步骤内容 */}
<div className="min-h-[400px]">
  {currentStep === 0 && renderStep1()}
  {currentStep === 1 && hasAppConfig && renderStep2()}
  {currentStep === 1 && !hasAppConfig && renderStep3()}
  {currentStep === 2 && hasAppConfig && renderStep3()}
</div>
```

### 5. 修复按钮逻辑

```javascript
{/* 判断是否是最后一步 */}
{(currentStep === 1 && !hasAppConfig) || (currentStep === 2 && hasAppConfig) ? (
  <Button type="primary" loading={loading} onClick={handleConfirm}>
    {loading ? '实例创建中...' : '确认订购'}
  </Button>
) : (
  <Button
    type="primary"
    onClick={handleNext}
    icon={<ArrowRight className="w-4 h-4" />}
  >
    下一步
  </Button>
)}
```

## 修复后的流程

### 有应用配置的情况 (hasAppConfig = true):
1. **步骤 0**: 确认配置 → 点击"下一步"
2. **步骤 1**: 应用配置 → 点击"下一步"  
3. **步骤 2**: 订单确认 → 点击"确认订购"

### 无应用配置的情况 (hasAppConfig = false):
1. **步骤 0**: 确认配置 → 点击"下一步"
2. **步骤 1**: 订单确认 → 点击"确认订购"

## 测试场景

1. **选择应用并启用自动安装**: 应该显示3个步骤
2. **选择应用但禁用自动安装**: 应该显示2个步骤
3. **不选择应用**: 应该显示2个步骤
4. **在第一步切换自动安装开关**: 步骤数应该动态调整
5. **步骤间导航**: 前进/后退按钮应该正常工作

## 验证方法

1. 打开算力市场页面
2. 选择一个设备点击"立即租用"
3. 在第一步选择一个应用
4. 确保"自动安装"开关是开启的
5. 点击"下一步"，应该进入应用配置步骤
6. 配置应用参数和环境变量
7. 点击"下一步"，应该进入订单确认步骤
8. 点击"确认订购"完成流程
