import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { Form } from 'antd';
import EnvironmentVariablesInput from '../EnvironmentVariablesInput';

// 测试组件包装器
const TestWrapper = ({ initialValues = [], onValuesChange }) => {
  const [form] = Form.useForm();
  
  return (
    <Form 
      form={form} 
      initialValues={{ environmentVariables: initialValues }}
      onValuesChange={onValuesChange}
    >
      <EnvironmentVariablesInput />
    </Form>
  );
};

describe('EnvironmentVariablesInput', () => {
  test('renders with default empty state', () => {
    render(<TestWrapper />);
    
    expect(screen.getByText('暂无环境变量配置')).toBeInTheDocument();
    expect(screen.getByText('添加环境变量')).toBeInTheDocument();
  });

  test('can add new environment variable', async () => {
    render(<TestWrapper />);
    
    // 点击添加按钮
    fireEvent.click(screen.getByText('添加环境变量'));
    
    await waitFor(() => {
      expect(screen.getByPlaceholderText('如: DATABASE_URL')).toBeInTheDocument();
      expect(screen.getByPlaceholderText('如: mysql://localhost:3306/db')).toBeInTheDocument();
    });
  });

  test('validates environment variable key format', async () => {
    render(<TestWrapper />);
    
    // 添加环境变量
    fireEvent.click(screen.getByText('添加环境变量'));
    
    await waitFor(() => {
      const keyInput = screen.getByPlaceholderText('如: DATABASE_URL');
      
      // 输入无效的变量名
      fireEvent.change(keyInput, { target: { value: 'invalid-key' } });
      fireEvent.blur(keyInput);
    });
    
    await waitFor(() => {
      expect(screen.getByText('变量名只能包含大写字母、数字和下划线，且以字母或下划线开头')).toBeInTheDocument();
    });
  });

  test('accepts valid environment variable key format', async () => {
    render(<TestWrapper />);
    
    // 添加环境变量
    fireEvent.click(screen.getByText('添加环境变量'));
    
    await waitFor(() => {
      const keyInput = screen.getByPlaceholderText('如: DATABASE_URL');
      const valueInput = screen.getByPlaceholderText('如: mysql://localhost:3306/db');
      
      // 输入有效的变量名和值
      fireEvent.change(keyInput, { target: { value: 'DATABASE_URL' } });
      fireEvent.change(valueInput, { target: { value: 'mysql://localhost:3306/test' } });
    });
    
    // 不应该有错误消息
    expect(screen.queryByText('变量名只能包含大写字母、数字和下划线，且以字母或下划线开头')).not.toBeInTheDocument();
  });

  test('can remove environment variable', async () => {
    render(<TestWrapper initialValues={[{ key: 'TEST_VAR', value: 'test_value' }]} />);
    
    await waitFor(() => {
      expect(screen.getByDisplayValue('TEST_VAR')).toBeInTheDocument();
      expect(screen.getByDisplayValue('test_value')).toBeInTheDocument();
    });
    
    // 点击删除按钮
    const deleteButton = screen.getByRole('button', { name: /minus-circle/i });
    fireEvent.click(deleteButton);
    
    await waitFor(() => {
      expect(screen.queryByDisplayValue('TEST_VAR')).not.toBeInTheDocument();
      expect(screen.queryByDisplayValue('test_value')).not.toBeInTheDocument();
    });
  });

  test('shows help text when variables exist', async () => {
    render(<TestWrapper />);
    
    // 添加环境变量
    fireEvent.click(screen.getByText('添加环境变量'));
    
    await waitFor(() => {
      const keyInput = screen.getByPlaceholderText('如: DATABASE_URL');
      const valueInput = screen.getByPlaceholderText('如: mysql://localhost:3306/db');
      
      fireEvent.change(keyInput, { target: { value: 'TEST_VAR' } });
      fireEvent.change(valueInput, { target: { value: 'test_value' } });
    });
    
    await waitFor(() => {
      expect(screen.getByText('💡 提示：环境变量将在应用启动时注入到容器中，请确保变量名和值的正确性。')).toBeInTheDocument();
    });
  });

  test('can be disabled', () => {
    const TestDisabledWrapper = () => (
      <Form>
        <EnvironmentVariablesInput disabled={true} />
      </Form>
    );
    
    render(<TestDisabledWrapper />);
    
    // 添加按钮应该不存在（因为被禁用）
    expect(screen.queryByText('添加环境变量')).not.toBeInTheDocument();
  });

  test('supports custom title', () => {
    const TestCustomTitleWrapper = () => (
      <Form>
        <EnvironmentVariablesInput title="自定义环境变量" />
      </Form>
    );
    
    render(<TestCustomTitleWrapper />);
    
    expect(screen.getByText('自定义环境变量')).toBeInTheDocument();
  });

  test('can hide title', () => {
    const TestNoTitleWrapper = () => (
      <Form>
        <EnvironmentVariablesInput showTitle={false} />
      </Form>
    );
    
    render(<TestNoTitleWrapper />);
    
    expect(screen.queryByText('环境变量配置')).not.toBeInTheDocument();
  });
});
